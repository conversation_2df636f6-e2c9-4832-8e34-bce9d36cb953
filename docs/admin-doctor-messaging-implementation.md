# Admin-Doctor Messaging System Implementation Guide

## Overview

This document outlines the implementation of the admin-doctor messaging system that allows admins and doctors to communicate about patient cases, bypassing the message router system.

## Key Features Implemented

### Core Functionality

- ✅ One conversation per patient between doctor and admin
- ✅ Conversation status: Open/Closed using dedicated `open` and `closed` status values
- ✅ Assignment status: Assigned/Unassigned (only one admin can be assigned)
- ✅ Photo upload support (reuses existing file upload infrastructure)
- ✅ Messages bypass the message router system
- ✅ Auto-assignment when admin sends message
- ✅ Auto-unassignment when conversation closes

### Database Changes

- ✅ Added new conversation status values: `open` and `closed` for admin-doctor conversations
- ✅ Modified `Conversation` table to support multiple conversation types per patient
- ✅ Added `assignedAdminId` field for admin assignment
- ✅ Added `closedAt` field for tracking conversation closure
- ✅ Composite unique constraint `(patientId, type)` ensures one conversation per patient per type
- ✅ Performance indexes for admin queries

### API Endpoints

- ✅ `POST /admin-doctor-chat/conversations` - Create admin-doctor conversation
- ✅ `GET /admin-doctor-chat/conversations` - Get admin conversations with filtering
- ✅ `PUT /admin-doctor-chat/conversations/:id/assign` - Assign conversation to admin
- ✅ `PUT /admin-doctor-chat/conversations/:id/unassign` - Unassign conversation
- ✅ `PUT /admin-doctor-chat/conversations/:id/close` - Close conversation
- ✅ `PUT /admin-doctor-chat/conversations/:id/reopen` - Reopen conversation

## Implementation Steps

### Phase 1: Database Migration (REQUIRED FIRST)

1. **Run the migration script:**

   ```bash
   npx ts-node scripts/migrate-admin-doctor-messaging.ts
   ```

2. **Regenerate Prisma types:**

   ```bash
   cd packages/db
   npx prisma generate
   ```

3. **Update Prisma schema (already done):**
   - Modified `Conversation` model with new fields
   - Updated unique constraints
   - Added performance indexes

### Phase 2: Backend Implementation (COMPLETED)

1. **Use Cases Created:**

   - `CreateAdminDoctorConversationUseCase` - Creates admin-doctor conversations
   - `GetAdminConversationsUseCase` - Fetches conversations with filtering
   - `ManageAdminConversationAssignmentUseCase` - Handles assignment/closure

2. **Message Router Bypass:**

   - Updated `SendMessageUseCase` to bypass router for `doctorAdmin` conversations
   - Auto-assignment logic when admin sends messages

3. **API Controller:**

   - `AdminDoctorChatController` with all required endpoints
   - Proper role-based access control

4. **Conversation Status Management:**
   - `open` - Admin-doctor conversations that are active and open
   - `closed` - Admin-doctor conversations that have been closed by admin
   - Existing states (`active`, `needsReply`, `followup`) continue to be used for patient-doctor conversations
   - Clear separation between admin-doctor and patient-doctor conversation states

### Phase 3: Frontend Implementation (TODO)

#### Admin Interface Requirements:

1. **Patient Profile Integration:**

   ```typescript
   // Add admin-doctor chat access from patient profile
   // Button/link to open admin-doctor conversation
   ```

2. **Messages Tab with Filters:**

   ```typescript
   // Implement tabs for:
   // - My Inbox (assigned conversations)
   // - All (all open conversations)
   // - Unassigned (unassigned open conversations)
   // - Closed (conversations closed in last 7 days)
   ```

3. **Admin Actions:**
   ```typescript
   // Implement UI for:
   // - Assign/Unassign conversations
   // - Open/Close conversations
   // - Patient profile slide-out
   ```

#### Doctor Interface Requirements:

1. **Patient Profile Integration:**

   ```typescript
   // Add admin-chat access from patient profile
   // Button/link to initiate admin conversation
   ```

2. **Message Swimlane Updates:**
   ```typescript
   // Handle admin responses with lighter blue bar
   // Show patient name as "User" for admin conversations
   // Support 2 cards per patient (patient chat + admin chat)
   ```

## API Usage Examples

### Create Admin-Doctor Conversation

```typescript
POST /admin-doctor-chat/conversations
{
  "patientId": "patient-uuid",
  "doctorUserId": "doctor-user-uuid"
}
```

### Get Admin Conversations

```typescript
GET /admin-doctor-chat/conversations?filter=myInbox&page=1&limit=20
```

### Assign Conversation

```typescript
PUT /admin-doctor-chat/conversations/{conversationId}/assign
{
  "adminUserId": "admin-user-uuid" // Optional, defaults to current user
}
```

### Send Message (uses existing endpoint)

```typescript
POST /chat/conversations/{conversationId}/messages
{
  "content": "Message content",
  "contentType": "text",
  "type": "message"
}
```

## Backward Compatibility

### Patient-Facing Endpoints

- ✅ All existing patient endpoints continue to work identically
- ✅ Patients only see `patientDoctor` conversations
- ✅ Same property names and response structures maintained
- ✅ No breaking changes to existing functionality

### Doctor Endpoints

- ✅ Existing doctor conversation access unchanged
- ✅ Doctor patient lists continue to work
- ✅ Conversation creation maintains compatibility

## Security Considerations

### Access Control

- ✅ Admin-doctor conversations require `Admin` or `Doctor` role
- ✅ Patients cannot access `doctorAdmin` conversation types
- ✅ Conversation watchers control message visibility
- ✅ Role-based endpoint protection

### Data Isolation

- ✅ Strict filtering by conversation type in all queries
- ✅ Composite unique constraint prevents data conflicts
- ✅ Foreign key constraints maintain data integrity

## Testing Recommendations

### Backend Testing

1. **Migration Testing:**

   - Test migration script on development database
   - Verify existing conversations maintain functionality
   - Confirm new constraints work correctly

2. **API Testing:**

   - Test all new endpoints with proper authentication
   - Verify role-based access control
   - Test conversation creation and management

3. **Integration Testing:**
   - Test message sending in admin-doctor conversations
   - Verify message router bypass
   - Test auto-assignment functionality

### Frontend Testing

1. **Admin Interface:**

   - Test conversation filtering and pagination
   - Verify assignment/unassignment functionality
   - Test patient profile integration

2. **Doctor Interface:**
   - Test admin conversation initiation
   - Verify message swimlane updates
   - Test multiple conversation cards per patient

## Deployment Checklist

- [ ] Run database migration script
- [ ] Regenerate Prisma types
- [ ] Deploy backend changes
- [ ] Test API endpoints
- [ ] Implement frontend components
- [ ] Test end-to-end functionality
- [ ] Monitor for any issues

## Support and Troubleshooting

### Common Issues

1. **Migration Fails:** Check database permissions and existing constraints
2. **Type Errors:** Regenerate Prisma types after schema changes
3. **Access Denied:** Verify role-based permissions are correctly configured

### Rollback Plan

If issues arise, the migration can be partially rolled back by:

1. Removing new indexes
2. Restoring unique constraints on `userId` and `patientId`
3. Setting all conversation types back to `patientDoctor`

Note: Complete rollback may require data cleanup if admin-doctor conversations were created.
