import { PrismaService } from '@/modules/prisma/prisma.service';
import { Test, TestingModule } from '@nestjs/testing';

import { CreateAdminDoctorConversationUseCase } from '../create-admin-doctor-conversation.use-case';
import { GetAdminConversationsUseCase } from '../get-admin-conversations.use-case';
import { ManageAdminConversationAssignmentUseCase } from '../manage-admin-conversation-assignment.use-case';
import { SendMessageUseCase } from '../send-message.use-case';

describe('Admin-Doctor Messaging Integration Tests', () => {
  let module: TestingModule;
  let prismaService: PrismaService;
  let createAdminDoctorConversationUseCase: CreateAdminDoctorConversationUseCase;
  let getAdminConversationsUseCase: GetAdminConversationsUseCase;
  let manageAdminConversationAssignmentUseCase: ManageAdminConversationAssignmentUseCase;
  let sendMessageUseCase: SendMessageUseCase;

  // Test data
  let testPatientId: string;
  let testAdminUserId: string;
  let testDoctorUserId: string;
  let testConversationId: string;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        PrismaService,
        CreateAdminDoctorConversationUseCase,
        GetAdminConversationsUseCase,
        ManageAdminConversationAssignmentUseCase,
        // Note: SendMessageUseCase would need additional dependencies for full testing
      ],
    }).compile();

    prismaService = module.get<PrismaService>(PrismaService);
    createAdminDoctorConversationUseCase =
      module.get<CreateAdminDoctorConversationUseCase>(
        CreateAdminDoctorConversationUseCase,
      );
    getAdminConversationsUseCase = module.get<GetAdminConversationsUseCase>(
      GetAdminConversationsUseCase,
    );
    manageAdminConversationAssignmentUseCase =
      module.get<ManageAdminConversationAssignmentUseCase>(
        ManageAdminConversationAssignmentUseCase,
      );
  });

  beforeEach(async () => {
    // Create test data
    const adminUser = await prismaService.user.create({
      data: {
        id: 'test-admin-user-id',
        firstName: 'Test',
        lastName: 'Admin',
        email: '<EMAIL>',
        type: 'admin',
        admin: {
          create: {
            role: 'admin',
          },
        },
      },
    });

    const doctorUser = await prismaService.user.create({
      data: {
        id: 'test-doctor-user-id',
        firstName: 'Test',
        lastName: 'Doctor',
        email: '<EMAIL>',
        type: 'doctor',
        doctor: {
          create: {
            doseSpotClinicianId: 'test-clinician-id',
            address1: '123 Test St',
            city: 'Test City',
            zip: '12345',
            state: {
              connect: { code: 'CA' },
            },
          },
        },
      },
    });

    const patientUser = await prismaService.user.create({
      data: {
        id: 'test-patient-user-id',
        firstName: 'Test',
        lastName: 'Patient',
        email: '<EMAIL>',
        type: 'patient',
      },
    });

    const patient = await prismaService.patient.create({
      data: {
        id: 'test-patient-id',
        userId: patientUser.id,
        stateId: 'test-state-id',
        questionnaireId: 'test-questionnaire-id',
        status: 'onboardingCompleted',
      },
    });

    testPatientId = patient.id;
    testAdminUserId = adminUser.id;
    testDoctorUserId = doctorUser.id;
  });

  afterEach(async () => {
    // Clean up test data
    await prismaService.conversationWatcher.deleteMany({
      where: {
        OR: [{ userId: testAdminUserId }, { userId: testDoctorUserId }],
      },
    });
    await prismaService.conversation.deleteMany({
      where: { patientId: testPatientId },
    });
    await prismaService.patient.deleteMany({
      where: { id: testPatientId },
    });
    await prismaService.admin.deleteMany({
      where: { userId: testAdminUserId },
    });
    await prismaService.doctor.deleteMany({
      where: { userId: testDoctorUserId },
    });
    await prismaService.user.deleteMany({
      where: {
        id: {
          in: [testAdminUserId, testDoctorUserId, 'test-patient-user-id'],
        },
      },
    });
  });

  afterAll(async () => {
    await module.close();
  });

  describe('CreateAdminDoctorConversationUseCase', () => {
    it('should create a new admin-doctor conversation', async () => {
      const conversationId = await createAdminDoctorConversationUseCase.execute(
        {
          patientId: testPatientId,
          adminUserId: testAdminUserId,
          doctorUserId: testDoctorUserId,
        },
      );

      expect(conversationId).toBeDefined();
      testConversationId = conversationId;

      // Verify conversation was created with correct type
      const conversation = await prismaService.conversation.findUnique({
        where: { id: conversationId },
        include: { watcher: true },
      });

      expect(conversation).toBeDefined();
      expect(conversation.type).toBe('doctorAdmin');
      expect(conversation.patientId).toBe(testPatientId);
      expect(conversation.assignedAdminId).toBe(testAdminUserId);
      expect(conversation.watcher).toHaveLength(2);
    });

    it('should return existing conversation if one already exists', async () => {
      // Create first conversation
      const firstConversationId =
        await createAdminDoctorConversationUseCase.execute({
          patientId: testPatientId,
          adminUserId: testAdminUserId,
          doctorUserId: testDoctorUserId,
        });

      // Try to create second conversation
      const secondConversationId =
        await createAdminDoctorConversationUseCase.execute({
          patientId: testPatientId,
          adminUserId: testAdminUserId,
          doctorUserId: testDoctorUserId,
        });

      expect(firstConversationId).toBe(secondConversationId);
    });
  });

  describe('GetAdminConversationsUseCase', () => {
    beforeEach(async () => {
      // Create test conversation
      testConversationId = await createAdminDoctorConversationUseCase.execute({
        patientId: testPatientId,
        adminUserId: testAdminUserId,
        doctorUserId: testDoctorUserId,
      });
    });

    it('should get admin conversations with myInbox filter', async () => {
      const result = await getAdminConversationsUseCase.execute({
        adminUserId: testAdminUserId,
        filter: 'myInbox',
      });

      expect(result.conversation).toHaveLength(1);
      expect(result.conversation[0].id).toBe(testConversationId);
      expect(result.conversation[0].assignedAdminId).toBe(testAdminUserId);
    });

    it('should get admin conversations with all filter', async () => {
      const result = await getAdminConversationsUseCase.execute({
        adminUserId: testAdminUserId,
        filter: 'all',
      });

      expect(result.conversation).toHaveLength(1);
      expect(result.conversation[0].id).toBe(testConversationId);
    });

    it('should handle pagination correctly', async () => {
      const result = await getAdminConversationsUseCase.execute({
        adminUserId: testAdminUserId,
        filter: 'all',
        page: 1,
        limit: 10,
      });

      expect(result.pagination.currentPage).toBe(1);
      expect(result.pagination.totalCount).toBe(1);
      expect(result.pagination.hasNextPage).toBe(false);
    });
  });

  describe('ManageAdminConversationAssignmentUseCase', () => {
    beforeEach(async () => {
      // Create test conversation
      testConversationId = await createAdminDoctorConversationUseCase.execute({
        patientId: testPatientId,
        adminUserId: testAdminUserId,
        doctorUserId: testDoctorUserId,
      });
    });

    it('should assign conversation to admin', async () => {
      const result =
        await manageAdminConversationAssignmentUseCase.assignConversation({
          conversationId: testConversationId,
          adminUserId: testAdminUserId,
        });

      expect(result.assignedAdminId).toBe(testAdminUserId);
    });

    it('should unassign conversation', async () => {
      const result =
        await manageAdminConversationAssignmentUseCase.unassignConversation({
          conversationId: testConversationId,
        });

      expect(result.assignedAdminId).toBeNull();
    });

    it('should close conversation and auto-unassign', async () => {
      const result =
        await manageAdminConversationAssignmentUseCase.closeConversation({
          conversationId: testConversationId,
          adminUserId: testAdminUserId,
        });

      expect(result.status).toBe('closed');
      expect(result.assignedAdminId).toBeNull();
      expect(result.closedAt).toBeDefined();
    });

    it('should reopen conversation', async () => {
      // First close the conversation
      await manageAdminConversationAssignmentUseCase.closeConversation({
        conversationId: testConversationId,
        adminUserId: testAdminUserId,
      });

      // Then reopen it
      const result =
        await manageAdminConversationAssignmentUseCase.reopenConversation({
          conversationId: testConversationId,
        });

      expect(result.status).toBe('open');
      expect(result.closedAt).toBeNull();
    });
  });
});
