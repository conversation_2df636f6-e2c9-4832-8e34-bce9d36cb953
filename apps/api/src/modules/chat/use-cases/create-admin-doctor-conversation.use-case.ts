import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateAdminDoctorConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute({
    patientId,
    adminUserId,
    doctorUserId,
  }: {
    patientId: string;
    adminUserId: string;
    doctorUserId: string;
  }) {
    // Check if admin-doctor conversation already exists for this patient
    const existingConversation =
      await this.prismaService.conversation.findFirst({
        where: {
          patientId,
          type: 'doctorAdmin',
        },
        include: { watcher: true },
      });

    if (existingConversation) {
      // Check if both admin and doctor are already watchers
      const hasAdminWatcher = existingConversation.watcher.some(
        (watcher) => watcher.userId === adminUserId,
      );
      const hasDoctorWatcher = existingConversation.watcher.some(
        (watcher) => watcher.userId === doctorUserId,
      );

      if (hasAdminWatcher && hasDoctorWatcher) {
        return existingConversation.id;
      }

      // Add missing watchers
      if (!hasAdminWatcher) {
        await this.prismaService.conversationWatcher.create({
          data: {
            userId: adminUserId,
            conversationId: existingConversation.id,
          },
        });
      }

      if (!hasDoctorWatcher) {
        await this.prismaService.conversationWatcher.create({
          data: {
            userId: doctorUserId,
            conversationId: existingConversation.id,
          },
        });
      }

      return existingConversation.id;
    }

    // Get patient information
    const patient = await this.prismaService.patient.findUnique({
      where: { id: patientId },
      include: { user: true },
    });

    if (!patient) {
      throw new Error(`Patient not found: ${patientId}`);
    }

    // Create new admin-doctor conversation
    return this.prismaService.$transaction(async (prisma) => {
      const conversation = await prisma.conversation.create({
        data: {
          userId: patient.userId, // Still point to patient user for consistency
          patientId: patient.id,
          type: 'doctorAdmin',
          status: 'open', // Use 'open' status for admin-doctor conversations
          assignedAdminId: adminUserId, // Auto-assign to the admin who creates it
          updatedAt: null,
        },
      });

      // Create watchers for admin and doctor
      await Promise.all([
        prisma.conversationWatcher.create({
          data: {
            userId: adminUserId,
            conversationId: conversation.id,
          },
        }),
        prisma.conversationWatcher.create({
          data: {
            userId: doctorUserId,
            conversationId: conversation.id,
          },
        }),
      ]);

      return conversation.id;
    });
  }
}
