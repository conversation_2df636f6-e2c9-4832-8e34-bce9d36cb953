import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { StripeService } from '@/modules/stripe/service/stripe.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetPatientByIdUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
    private readonly stripeService: StripeService,
  ) {}

  async execute(patientId: string) {
    const [patient, paymentMethod] = await Promise.all([
      this.prismaService.patient.findUniqueOrThrow({
        where: {
          id: patientId,
        },
        select: {
          id: true,
          userId: true,
          doseSpotPatientId: true,
          birthDate: true,
          gender: true,
          height: true,
          weight: true,
          idPhoto: true,
          facePhoto: true,
          status: true,
          statusBeforeCancellation: true,
          stripeCustomerId: true,
          verificationStatus: true,
          rejectedStatus: true,
          rejectedReason: true,
          createdAt: true,
          updatedAt: true,
          acceptedAt: true,
          canceledAt: true,
          canceledBy: true,
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              phone: true,
              createdAt: true,
            },
          },
          doctor: {
            select: {
              id: true,
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          state: {
            select: {
              name: true,
              code: true,
              id: true,
            },
          },
          pharmacy: {
            select: {
              id: true,
              name: true,
            },
          },
          shippingAddresses: {
            select: {
              id: true,
              address1: true,
              address2: true,
              city: true,
              zip: true,
              state: {
                select: {
                  code: true,
                },
              },
            },
            where: { default: true },
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          conversations: {
            where: { type: 'patientDoctor' },
            select: {
              id: true,
              updatedAt: true,
            },
          },
          PatientFollowUp: {
            select: {
              scheduledAt: true,
            },
            where: {
              status: 'scheduled',
              scheduledAt: {
                gte: new Date(),
              },
            },
          },
        },
      }),
      this.patientPaymentMethodPersistence.getDefaultPaymentmethod(patientId),
    ]);

    let result = {
      ...patient,
      shippingAddress: patient.shippingAddresses[0] ?? null,
      paymentMethod,
      billingAddress: null,
    };

    if (patient.stripeCustomerId) {
      try {
        const stripeInfo = await this.stripeService.getCustomer(
          patient.stripeCustomerId,
        );

        result = {
          ...result,
          billingAddress: stripeInfo?.address
            ? {
                address1: stripeInfo?.address.line1,
                address2: stripeInfo?.address.line2,
                city: stripeInfo?.address.city,
                zip: stripeInfo?.address.postal_code,
                state: {
                  code: stripeInfo?.address.state,
                },
              }
            : null,
        };
      } catch (e) {
        console.error('Error retrieving Stripe customer:', e);
      }
    }

    delete result.shippingAddresses;

    return result;
  }
}
