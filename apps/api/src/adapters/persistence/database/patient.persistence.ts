import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@/modules/prisma/prisma.service';
import { DoctorProfile } from '@adapters/persistence/database/doctor.persistence';
import { OnboardingSnapshot } from '@modules/onboarding/onboarding-state.service';
import {
  TreatmentService,
  TreatmentSnapshot,
} from '@modules/treatment/services/treatment.service';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Conversation, Patient, Prisma, State, User } from '@prisma/client';

export type PatientOnboardingProfile = Awaited<
  ReturnType<typeof PatientPersistence.prototype.getOnboardingProfile>
>;

export type PatientDashboardProfile = Awaited<
  ReturnType<typeof PatientPersistence.prototype.getDashboardProfile>
>;

export type PatientProfile = Awaited<
  ReturnType<typeof PatientPersistence.prototype.getPatientProfile>
>;

export type PatientFollowUpProfile = Awaited<
  ReturnType<typeof PatientPersistence.prototype.getFollowUpProfile>
>;

type PatientWithRelations = Patient & {
  user: User & { conversations: Conversation[] };
  state: State;
};
type PendingPatients = {
  pendingApprovalPatients: PatientWithRelations[];
  revalidatedPatients: PatientWithRelations[];
};

@Injectable()
export class PatientPersistence {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(forwardRef(() => TreatmentService))
    private readonly treatmentService: TreatmentService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
  ) {}

  async getOnboardingProfile(userId: string) {
    return this.prisma.patient.findFirstOrThrow({
      where: {
        id: userId,
        user: { type: 'patient' },
      },
      include: {
        user: true,
        questionnaire: true,
        desiredTreatments: {
          include: {
            product: true,
          },
        },
        shippingAddresses: true,
        paymentMethods: true,
        state: true,
        pharmacy: true,
      },
    });
  }

  async getFollowUpProfile(userId: string) {
    return this.prisma.patient.findFirstOrThrow({
      include: { user: { select: { id: true } } },
      where: { userId: userId, user: { type: 'patient' } },
    });
  }

  async getPatientProfile(
    id: string,
    { prisma }: { prisma: PrismaTransactionalClient } = { prisma: this.prisma },
  ) {
    return prisma.patient.findFirstOrThrow({
      where: { id: id },
      include: {
        user: true,
        doctor: true,
      },
    });
  }

  async getDashboardProfile(userId: string) {
    // @todo move this and other methods to a Patient Service from Patient module
    const patient = await this.prisma.patient.findFirstOrThrow({
      where: {
        user: { id: userId, type: 'patient', deletedAt: null },
        status: {
          in: [
            'pendingUploadPhotos',
            'onboardingCompleted',
            'pendingApprovalFromDoctor',
            'pendingPrescription',
            'activePrescription',
            'nonActivePrescription',
            'cancelled',
          ],
        },
      },
      select: {
        id: true,
        stripeCustomerId: true,
        doseSpotPatientId: true,
        intercomContactId: true,
        idPhoto: true,
        facePhoto: true,
        status: true,
        statusBeforeCancellation: true,
        verificationStatus: true,
        rejectedStatus: true,
        rejectedReason: true,
        gender: true,
        birthDate: true,
        state: true,
        referralCode: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        },
        doctor: {
          select: {
            id: true,
            image: true,
            doseSpotClinicianId: true,
            user: { select: { id: true, firstName: true, lastName: true } },
          },
        },
        conversations: {
          where: { type: 'patientDoctor' }, // Only include patient-doctor conversations
          include: {
            watcher: {
              where: { userId },
              select: { unreadMessages: true, updatedAt: true },
            },
          },
        },
        treatment: {
          where: {
            isCore: true,
            OR: [
              { status: { startsWith: 'inProgress' } },
              { status: 'paused' },
            ],
          },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
        subscriptions: {
          where: {
            endDate: {
              gte: new Date(),
            },
          },
          select: {
            id: true,
            name: true,
            endDate: true,
            startDate: true,
            status: true,
          },
        },
      },
    });
    const paymentMethod =
      await this.patientPaymentMethodPersistence.getDefaultPaymentmethod(
        patient.id,
      );

    let treatment: {
      name: string;
      form: string;
      dose: string;
      dosageDescription: string;
      dosageTimeframe: string;
    } | null = null;

    if (patient.treatment.length > 0) {
      const actor = await this.treatmentService.getActor(
        patient.treatment[0].id,
        (e) => {
          console.log('[getDashboardProfile] emitEvent', e);
        },
        patient.treatment[0].state as unknown as TreatmentSnapshot,
      );

      const activeProduct = actor.getSnapshot().context.activeProduct;
      const price = await this.prisma.productPrice.findFirstOrThrow({
        where: {
          id: activeProduct.id,
        },
      });
      treatment = {
        name: activeProduct.name,
        form: activeProduct.form,
        dose: activeProduct.dose,
        dosageDescription: price.dosageDescription,
        dosageTimeframe: price.dosageTimeframe,
      };
    }

    return {
      ...patient,
      treatment,
      paymentMethod,
    };
  }

  async getOnboardingProfileByStripeCustomer(stripeCustomerId: string) {
    return this.prisma.patient.findFirstOrThrow({
      where: {
        stripeCustomerId,
        user: { type: 'patient' },
      },
      include: {
        user: true,
        questionnaire: true,
        desiredTreatments: {
          include: {
            product: true,
          },
        },
        shippingAddresses: true,
        paymentMethods: true,
        state: true,
        pharmacy: true,
      },
    });
  }

  async update(
    patientId: string,
    data: Prisma.PatientUpdateInput,
    client?: PrismaTransactionalClient,
  ) {
    return (client || this.prisma).patient.update({
      where: { id: patientId },
      data,
    });
  }

  async updateOnboarding(
    patientId: string,
    state: OnboardingSnapshot,
    data?: Omit<Prisma.PatientUpdateInput, 'onboardingState'>,
    client?: PrismaTransactionalClient,
  ) {
    return (client || this.prisma).patient.update({
      where: { id: patientId },
      data: { onboardingState: sanitizeMachineStateForDB(state), ...data },
    });
  }

  async rejectOnboarding(
    patientId: string,
    state: OnboardingSnapshot,
    reason: string,
  ) {
    return this.prisma.patient.update({
      where: { id: patientId },
      data: {
        onboardingState: sanitizeMachineStateForDB(state),
        status: 'onboardingRejected',
        rejectedReason: reason,
      },
    });
  }

  async getAvailablePatientsForDoctor(doctor: DoctorProfile) {
    return this.prisma.patient.findMany({
      where: {
        user: { deletedAt: null },
        status: 'onboardingCompleted',
        doctorId: null,
        stateId: {
          in: doctor.prescribesIn.map((state) => state.stateId),
        },
      },
      include: {
        user: true,
        state: true,
        shippingAddresses: { where: { default: true } },
      },
      orderBy: { completedAt: 'asc' },
    });
  }

  async getPendingApprovalPatientsForDoctor(doctor: DoctorProfile) {
    const getPendingApprovalPatients = await this.prisma.patient.findMany({
      where: {
        user: { deletedAt: null },
        doctorId: doctor.id,
        status: {
          in: ['pendingApprovalFromDoctor', 'pendingPrescription'],
        },
        verificationStatus: { not: 'rejected' },
        conversations: {
          none: {
            type: 'patientDoctor',
            lastMessageText: { not: null },
          },
        },
      },
      include: {
        user: { include: { conversations: true } },
        state: true,
        treatment: {
          select: {
            id: true,
          },
        },
        conversations: {
          where: { type: 'patientDoctor' },
          select: {
            type: true,
            messages: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
        historicAssignments: {
          where: { previousAssignmentId: { not: null } },
          include: {
            previousAssignment: {
              include: { doctor: { include: { user: true } } },
            },
            BulkTransfer: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
      orderBy: { acceptedAt: 'asc' },
    });

    return getPendingApprovalPatients
      .filter((p) => p.treatment.length === 0)
      .filter((p) => {
        const patientDoctorConversation = p.conversations.find(
          (c) => c.type === 'patientDoctor',
        );
        return (
          !patientDoctorConversation ||
          patientDoctorConversation.messages.length === 0
        );
      })
      .reduce<PendingPatients>(
        (pendingPatients, patient) => {
          if (patient.verificationStatus === 'revalidated') {
            pendingPatients.revalidatedPatients.push(patient);
          } else {
            pendingPatients.pendingApprovalPatients.push(patient);
          }
          return pendingPatients;
        },
        {
          pendingApprovalPatients: [],
          revalidatedPatients: [],
        },
      );
  }

  async getPatientById(patientId: string) {
    return this.prisma.patient.findFirstOrThrow({
      where: {
        id: patientId,
      },
    });
  }
}

// Onbording was our first state machine, we made the mistake of dumping the entire state into the database
// caused prsima (with replica) to crash while updating the db
// this workaround removes all functions & circular references from the state
export function sanitizeMachineStateForDB<T>(state: T): T {
  return JSON.parse(JSON.stringify(state));
}
