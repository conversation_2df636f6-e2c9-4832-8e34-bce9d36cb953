/*
  Admin-Doctor Messaging System Migration

  This migration implements the admin-doctor messaging system by:
  1. Adding new conversation status values (open, closed)
  2. Removing unique constraints on userId and patientId in Conversation table
  3. Adding new fields for admin assignment and conversation closure
  4. Adding composite unique constraint (patientId, type)
  5. Adding indexes for efficient admin queries
  6. Setting default type for existing conversations
*/

-- Step 1: Add new conversation status values for admin-doctor conversations
ALTER TYPE "ConversationStatus" ADD VALUE IF NOT EXISTS 'open';
ALTER TYPE "ConversationStatus" ADD VALUE IF NOT EXISTS 'closed';

-- Step 2: Set default type for existing conversations to maintain backward compatibility
UPDATE "Conversation" SET "type" = 'patientDoctor' WHERE "type" IS NULL;

-- Step 3: Add new fields for admin functionality
ALTER TABLE "Conversation" ADD COLUMN "assignedAdminId" TEXT;
ALTER TABLE "Conversation" ADD COLUMN "closedAt" TIMESTAMP(3);

-- Step 4: Drop existing unique constraints to allow multiple conversations per patient
DROP INDEX IF EXISTS "Conversation_userId_key";
DROP INDEX IF EXISTS "Conversation_patientId_key";

-- Step 5: Add composite unique constraint to ensure one conversation per patient per type
CREATE UNIQUE INDEX "Conversation_patientId_type_key" ON "Conversation"("patientId", "type");

-- Step 6: Add performance indexes for admin queries
CREATE INDEX "Conversation_userId_type_idx" ON "Conversation"("userId", "type");
CREATE INDEX "Conversation_assignedAdminId_idx" ON "Conversation"("assignedAdminId");
CREATE INDEX "Conversation_type_status_idx" ON "Conversation"("type", "status");

-- Step 7: Add foreign key constraint for assignedAdmin
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_assignedAdminId_fkey"
  FOREIGN KEY ("assignedAdminId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
