#!/usr/bin/env ts-node

/**
 * Migration script for Admin-Doctor Messaging System
 *
 * This script implements the database changes needed for the admin-doctor messaging system:
 * 1. Removes unique constraints on userId and patientId in Conversation table
 * 2. Adds new fields for admin assignment and conversation closure
 * 3. Adds composite unique constraint (patientId, type)
 * 4. Adds indexes for efficient admin queries
 * 5. Sets default type for existing conversations
 *
 * Usage: npx ts-node scripts/migrate-admin-doctor-messaging.ts
 */
import { PrismaClient } from '@willow/db/client';

async function migrateAdminDoctorMessaging() {
  const prisma = new PrismaClient();

  try {
    console.log('🚀 Starting Admin-Doctor Messaging System migration...');

    // Step 1: Add new conversation status values for admin-doctor conversations
    console.log('📝 Step 1: Adding new conversation status values...');
    try {
      await prisma.$executeRaw`ALTER TYPE "ConversationStatus" ADD VALUE IF NOT EXISTS 'open';`;
      console.log('✅ Added "open" status value');
    } catch (error) {
      console.log('⚠️  "open" status value may already exist, continuing...');
    }

    try {
      await prisma.$executeRaw`ALTER TYPE "ConversationStatus" ADD VALUE IF NOT EXISTS 'closed';`;
      console.log('✅ Added "closed" status value');
    } catch (error) {
      console.log('⚠️  "closed" status value may already exist, continuing...');
    }

    // Step 2: Set default type for existing conversations to maintain backward compatibility
    console.log(
      '📝 Step 2: Setting default type for existing conversations...',
    );
    const updateResult = await prisma.$executeRaw`
      UPDATE "Conversation" SET "type" = 'patientDoctor' WHERE "type" IS NULL;
    `;
    console.log(`✅ Updated ${updateResult} conversations with default type`);

    // Step 3: Add new fields for admin functionality
    console.log('📝 Step 3: Adding new fields for admin functionality...');
    try {
      await prisma.$executeRaw`ALTER TABLE "Conversation" ADD COLUMN "assignedAdminId" TEXT;`;
      console.log('✅ Added assignedAdminId column');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️  assignedAdminId column already exists, skipping...');
      } else {
        throw error;
      }
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "Conversation" ADD COLUMN "closedAt" TIMESTAMP(3);`;
      console.log('✅ Added closedAt column');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️  closedAt column already exists, skipping...');
      } else {
        throw error;
      }
    }

    // Step 4: Drop existing unique constraints to allow multiple conversations per patient
    console.log('📝 Step 4: Dropping existing unique constraints...');
    try {
      await prisma.$executeRaw`DROP INDEX IF EXISTS "Conversation_userId_key";`;
      console.log('✅ Dropped userId unique constraint');
    } catch (error) {
      console.log('⚠️  userId unique constraint may not exist, continuing...');
    }

    try {
      await prisma.$executeRaw`DROP INDEX IF EXISTS "Conversation_patientId_key";`;
      console.log('✅ Dropped patientId unique constraint');
    } catch (error) {
      console.log(
        '⚠️  patientId unique constraint may not exist, continuing...',
      );
    }

    // Step 5: Add composite unique constraint to ensure one conversation per patient per type
    console.log('📝 Step 5: Adding composite unique constraint...');
    try {
      await prisma.$executeRaw`CREATE UNIQUE INDEX "Conversation_patientId_type_key" ON "Conversation"("patientId", "type");`;
      console.log('✅ Added composite unique constraint (patientId, type)');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log(
          '⚠️  Composite unique constraint already exists, skipping...',
        );
      } else {
        throw error;
      }
    }

    // Step 6: Add performance indexes for admin queries
    console.log('📝 Step 6: Adding performance indexes...');

    const indexes = [
      {
        name: 'Conversation_userId_type_idx',
        sql: 'CREATE INDEX IF NOT EXISTS "Conversation_userId_type_idx" ON "Conversation"("userId", "type");',
      },
      {
        name: 'Conversation_assignedAdminId_idx',
        sql: 'CREATE INDEX IF NOT EXISTS "Conversation_assignedAdminId_idx" ON "Conversation"("assignedAdminId");',
      },
      {
        name: 'Conversation_type_status_idx',
        sql: 'CREATE INDEX IF NOT EXISTS "Conversation_type_status_idx" ON "Conversation"("type", "status");',
      },
    ];

    for (const index of indexes) {
      try {
        await prisma.$executeRaw(index.sql as any);
        console.log(`✅ Added index: ${index.name}`);
      } catch (error) {
        console.log(`⚠️  Index ${index.name} may already exist, continuing...`);
      }
    }

    // Step 7: Add foreign key constraint for assignedAdmin
    console.log(
      '📝 Step 7: Adding foreign key constraint for assignedAdmin...',
    );
    try {
      await prisma.$executeRaw`
        ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_assignedAdminId_fkey" 
        FOREIGN KEY ("assignedAdminId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
      `;
      console.log('✅ Added foreign key constraint for assignedAdmin');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️  Foreign key constraint already exists, skipping...');
      } else {
        throw error;
      }
    }

    // Verify the migration
    console.log('📝 Step 8: Verifying migration...');
    const conversationCount = await prisma.conversation.count();
    const patientDoctorCount = await prisma.conversation.count({
      where: { type: 'patientDoctor' },
    });

    console.log(`✅ Total conversations: ${conversationCount}`);
    console.log(`✅ Patient-doctor conversations: ${patientDoctorCount}`);

    console.log(
      '🎉 Admin-Doctor Messaging System migration completed successfully!',
    );
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateAdminDoctorMessaging()
    .then(() => {
      console.log('✅ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateAdminDoctorMessaging };
